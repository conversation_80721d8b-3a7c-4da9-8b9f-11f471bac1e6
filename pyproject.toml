[project]
name = "pigeon-classifier"
version = "0.1.0"
description = ""
authors = [
    {name = "vsage",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "torch (>=2.7.1,<3.0.0)",
    "torchvision (>=0.22.1,<0.23.0)",
    "torchaudio (>=2.7.1,<3.0.0)",
    "pillow (>=11.3.0,<12.0.0)",
    "scikit-learn (>=1.7.0,<2.0.0)",
    "tqdm (>=4.67.1,<5.0.0)",
    "numpy (>=2.3.1,<3.0.0)"
]

[tool.poetry]
packages = [{include = "pigeon_classifier", from = "src"}]


[tool.poetry.group.dev.dependencies]
jupyter = "^1.1.1"
matplotlib = "^3.10.3"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
