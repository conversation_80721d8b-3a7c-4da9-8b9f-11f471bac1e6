#!/usr/bin/env python3
"""
Test script to verify the pigeon classifier setup.
"""
import os
from pathlib import Path
from src.pigeon_classifier.utils import load_pigeon_data

def main():
    print("🐦 Testing Pigeon Classifier Setup 🐦")
    print("=" * 50)
    
    # Check if data directory exists
    data_dir = "data/raw"
    if not os.path.exists(data_dir):
        print(f"❌ Data directory '{data_dir}' not found!")
        return
    
    print(f"✅ Data directory found: {data_dir}")
    
    # Load and analyze data
    try:
        train_paths, train_labels, val_paths, val_labels, class_to_idx, idx_to_class = load_pigeon_data(data_dir)
        
        print(f"✅ Successfully loaded pigeon data!")
        print(f"📊 Dataset Statistics:")
        print(f"   - Number of pigeon classes: {len(class_to_idx)}")
        print(f"   - Training samples: {len(train_paths)}")
        print(f"   - Validation samples: {len(val_paths)}")
        print(f"   - Total samples: {len(train_paths) + len(val_paths)}")
        
        print(f"\n🐦 Pigeon Classes Found:")
        for i, (pigeon_name, class_idx) in enumerate(sorted(class_to_idx.items()), 1):
            # Count images for this pigeon
            pigeon_dir = Path(data_dir) / pigeon_name
            image_count = len([f for f in pigeon_dir.iterdir() if f.suffix.lower() in {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}])
            print(f"   {i:2d}. {pigeon_name:<20} ({image_count} images)")
        
        # Check for pigeons with very few images
        print(f"\n⚠️  Recommendations:")
        low_image_pigeons = []
        for pigeon_name in class_to_idx.keys():
            pigeon_dir = Path(data_dir) / pigeon_name
            image_count = len([f for f in pigeon_dir.iterdir() if f.suffix.lower() in {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}])
            if image_count < 5:
                low_image_pigeons.append((pigeon_name, image_count))
        
        if low_image_pigeons:
            print(f"   - Consider adding more images for these pigeons:")
            for pigeon_name, count in low_image_pigeons:
                print(f"     • {pigeon_name}: {count} images (recommend 5+ images)")
        else:
            print(f"   - All pigeons have sufficient images for training!")
        
        print(f"\n🚀 Ready to train! Run:")
        print(f"   python -m pigeon_classifier.fine_tune")
        
    except Exception as e:
        print(f"❌ Error loading data: {str(e)}")
        return

if __name__ == "__main__":
    main()
