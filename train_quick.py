#!/usr/bin/env python3
"""
Quick training script with optimized settings for small datasets.
"""
import subprocess
import sys

def main():
    print("🐦 Starting Pigeon Classifier Training 🐦")
    print("=" * 50)
    print("Using optimized settings for small dataset...")
    
    # Training command with optimized parameters for small dataset
    cmd = [
        "poetry", "run", "python", "-m", "pigeon_classifier.fine_tune",
        "--data_dir", "data/raw",
        "--epochs", "30",           # More epochs for small dataset
        "--batch_size", "8",        # Smaller batch size
        "--learning_rate", "0.0001", # Lower learning rate
        "--test_size", "0.15"       # Smaller validation set
    ]
    
    print("Command:", " ".join(cmd))
    print("\nStarting training...")
    print("=" * 50)
    
    # Run the training
    try:
        result = subprocess.run(cmd, check=True)
        print("\n" + "=" * 50)
        print("🎉 Training completed successfully!")
        print("\nTo test your model, run:")
        print("poetry run python -m pigeon_classifier.predict --image_path path/to/your/image.jpg")
        
    except subprocess.CalledProcessError as e:
        print(f"\n❌ Training failed with error code {e.returncode}")
        print("Check the error messages above for details.")
        sys.exit(1)
    except KeyboardInterrupt:
        print("\n⚠️ Training interrupted by user.")
        sys.exit(1)

if __name__ == "__main__":
    main()
