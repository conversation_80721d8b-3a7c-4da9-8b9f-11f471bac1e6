import os
import glob
from pathlib import Path

def clean_image_folders():
    """Remove all .png files, Icon files, and desc.txt files from data/raw subfolders"""
    base_path = Path("data/raw")
    
    if not base_path.exists():
        print(f"Directory {base_path} not found")
        return
    
    removed_files = []
    
    # Iterate through all subdirectories in data/raw
    for folder in base_path.iterdir():
        if folder.is_dir():
            print(f"Cleaning folder: {folder.name}")
            
            # Remove all .png files
            png_files = list(folder.glob("*.png"))
            for png_file in png_files:
                png_file.unlink()
                removed_files.append(str(png_file))
                print(f"  Removed: {png_file.name}")
            
            # Remove Icon files (case-insensitive)
            icon_files = list(folder.glob("Icon*")) + list(folder.glob("icon*"))
            for icon_file in icon_files:
                icon_file.unlink()
                removed_files.append(str(icon_file))
                print(f"  Removed: {icon_file.name}")
            
            # Remove desc.txt files
            desc_files = list(folder.glob("desc.txt"))
            for desc_file in desc_files:
                desc_file.unlink()
                removed_files.append(str(desc_file))
                print(f"  Removed: {desc_file.name}")
    
    print(f"\nTotal files removed: {len(removed_files)}")
    return removed_files

if __name__ == "__main__":
    clean_image_folders()
