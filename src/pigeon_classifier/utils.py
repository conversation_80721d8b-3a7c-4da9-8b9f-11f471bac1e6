"""
Utility functions for pigeon classification.
"""
import os
import json
from pathlib import Path
from typing import Dict, List, Tuple, Optional
import torch
from torch.utils.data import Dataset, DataLoader
from torchvision import transforms
from PIL import Image
import numpy as np
from sklearn.model_selection import train_test_split


class PigeonDataset(Dataset):
    """Custom dataset for pigeon images."""
    
    def __init__(self, image_paths: List[str], labels: List[int], transform=None):
        self.image_paths = image_paths
        self.labels = labels
        self.transform = transform
    
    def __len__(self):
        return len(self.image_paths)
    
    def __getitem__(self, idx):
        image_path = self.image_paths[idx]
        image = Image.open(image_path).convert('RGB')
        label = self.labels[idx]
        
        if self.transform:
            image = self.transform(image)
        
        return image, label


def load_pigeon_data(data_dir: str, test_size: float = 0.2, random_state: int = 42) -> Tuple[List[str], List[int], List[str], List[int], Dict[str, int], Dict[int, str]]:
    """
    Load pigeon data from directory structure.
    
    Args:
        data_dir: Path to directory containing pigeon folders
        test_size: Fraction of data to use for testing
        random_state: Random seed for reproducibility
    
    Returns:
        train_paths, train_labels, val_paths, val_labels, class_to_idx, idx_to_class
    """
    data_path = Path(data_dir)
    
    # Get all pigeon classes (folder names)
    pigeon_classes = sorted([d.name for d in data_path.iterdir() if d.is_dir()])
    class_to_idx = {cls: idx for idx, cls in enumerate(pigeon_classes)}
    idx_to_class = {idx: cls for cls, idx in class_to_idx.items()}
    
    # Collect all image paths and labels
    all_paths = []
    all_labels = []
    
    for pigeon_class in pigeon_classes:
        class_dir = data_path / pigeon_class
        class_idx = class_to_idx[pigeon_class]
        
        # Get all image files in this class directory
        image_extensions = {'.jpg', '.jpeg', '.png', '.bmp', '.tiff'}
        for img_path in class_dir.iterdir():
            if img_path.suffix.lower() in image_extensions:
                all_paths.append(str(img_path))
                all_labels.append(class_idx)
    
    # Check if we can do stratified split (need at least 2 samples per class)
    from collections import Counter
    label_counts = Counter(all_labels)
    min_samples = min(label_counts.values())

    if min_samples >= 2:
        # Use stratified split if possible
        train_paths, val_paths, train_labels, val_labels = train_test_split(
            all_paths, all_labels, test_size=test_size, random_state=random_state, stratify=all_labels
        )
    else:
        # Use regular split if some classes have only 1 sample
        print(f"Warning: Some pigeon classes have only 1 image. Using non-stratified split.")
        train_paths, val_paths, train_labels, val_labels = train_test_split(
            all_paths, all_labels, test_size=test_size, random_state=random_state
        )
    
    return train_paths, train_labels, val_paths, val_labels, class_to_idx, idx_to_class


def get_transforms(input_size: int = 224, augment: bool = True):
    """
    Get image transforms for training and validation.
    
    Args:
        input_size: Size to resize images to
        augment: Whether to apply data augmentation
    
    Returns:
        train_transform, val_transform
    """
    if augment:
        train_transform = transforms.Compose([
            transforms.Resize((input_size, input_size)),
            transforms.RandomHorizontalFlip(p=0.5),
            transforms.RandomRotation(degrees=15),
            transforms.ColorJitter(brightness=0.2, contrast=0.2, saturation=0.2, hue=0.1),
            transforms.RandomAffine(degrees=0, translate=(0.1, 0.1), scale=(0.9, 1.1)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
    else:
        train_transform = transforms.Compose([
            transforms.Resize((input_size, input_size)),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
        ])
    
    val_transform = transforms.Compose([
        transforms.Resize((input_size, input_size)),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    return train_transform, val_transform


def save_model_and_mappings(model, class_to_idx: Dict[str, int], model_path: str):
    """
    Save the trained model and class mappings.
    
    Args:
        model: Trained PyTorch model
        class_to_idx: Dictionary mapping class names to indices
        model_path: Path to save the model
    """
    # Create directory if it doesn't exist
    os.makedirs(os.path.dirname(model_path), exist_ok=True)
    
    # Save model state dict
    torch.save(model.state_dict(), model_path)
    
    # Save class mappings
    mappings_path = model_path.replace('.pth', '_mappings.json')
    with open(mappings_path, 'w') as f:
        json.dump(class_to_idx, f, indent=2)
    
    print(f"Model saved to: {model_path}")
    print(f"Class mappings saved to: {mappings_path}")


def load_model_and_mappings(model, model_path: str) -> Tuple[torch.nn.Module, Dict[str, int], Dict[int, str]]:
    """
    Load the trained model and class mappings.
    
    Args:
        model: Model architecture (before loading weights)
        model_path: Path to the saved model
    
    Returns:
        loaded_model, class_to_idx, idx_to_class
    """
    # Load model weights
    model.load_state_dict(torch.load(model_path, map_location='cpu'))
    
    # Load class mappings
    mappings_path = model_path.replace('.pth', '_mappings.json')
    with open(mappings_path, 'r') as f:
        class_to_idx = json.load(f)
    
    idx_to_class = {int(idx): cls for cls, idx in class_to_idx.items()}
    
    return model, class_to_idx, idx_to_class


def predict_image(model, image_path: str, idx_to_class: Dict[int, str], transform, device: str = 'cpu', top_k: int = 5) -> List[Tuple[str, float]]:
    """
    Predict the class of a single image.
    
    Args:
        model: Trained model
        image_path: Path to the image
        idx_to_class: Dictionary mapping indices to class names
        transform: Image transform to apply
        device: Device to run inference on
        top_k: Number of top predictions to return
    
    Returns:
        List of (class_name, probability) tuples, sorted by probability (descending)
    """
    model.eval()
    
    # Load and preprocess image
    image = Image.open(image_path).convert('RGB')
    image_tensor = transform(image).unsqueeze(0).to(device)
    
    # Make prediction
    with torch.no_grad():
        outputs = model(image_tensor)
        probabilities = torch.nn.functional.softmax(outputs, dim=1)
        
    # Get top-k predictions
    top_probs, top_indices = torch.topk(probabilities, min(top_k, len(idx_to_class)))
    
    results = []
    for prob, idx in zip(top_probs[0], top_indices[0]):
        class_name = idx_to_class[idx.item()]
        probability = prob.item()
        results.append((class_name, probability))
    
    return results
