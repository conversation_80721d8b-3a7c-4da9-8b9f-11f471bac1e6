"""
Fine-tune a pre-trained model for pigeon classification.
"""
import argparse
import os
import time
from pathlib import Path
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader
from torchvision import models
from tqdm import tqdm
import numpy as np

from .utils import (
    load_pigeon_data, 
    get_transforms, 
    PigeonDataset, 
    save_model_and_mappings
)


def create_model(num_classes: int, pretrained: bool = True):
    """
    Create a ResNet50 model for pigeon classification.
    
    Args:
        num_classes: Number of pigeon classes
        pretrained: Whether to use pre-trained weights
    
    Returns:
        model: PyTorch model
    """
    model = models.resnet50(pretrained=pretrained)
    
    # Replace the final fully connected layer
    num_features = model.fc.in_features
    model.fc = nn.Linear(num_features, num_classes)
    
    return model


def train_epoch(model, dataloader, criterion, optimizer, device):
    """Train the model for one epoch."""
    model.train()
    running_loss = 0.0
    correct_predictions = 0
    total_samples = 0
    
    progress_bar = tqdm(dataloader, desc="Training")
    
    for images, labels in progress_bar:
        images, labels = images.to(device), labels.to(device)
        
        # Zero gradients
        optimizer.zero_grad()
        
        # Forward pass
        outputs = model(images)
        loss = criterion(outputs, labels)
        
        # Backward pass
        loss.backward()
        optimizer.step()
        
        # Statistics
        running_loss += loss.item()
        _, predicted = torch.max(outputs.data, 1)
        total_samples += labels.size(0)
        correct_predictions += (predicted == labels).sum().item()
        
        # Update progress bar
        progress_bar.set_postfix({
            'Loss': f'{loss.item():.4f}',
            'Acc': f'{100 * correct_predictions / total_samples:.2f}%'
        })
    
    epoch_loss = running_loss / len(dataloader)
    epoch_acc = correct_predictions / total_samples
    
    return epoch_loss, epoch_acc


def validate_epoch(model, dataloader, criterion, device):
    """Validate the model for one epoch."""
    model.eval()
    running_loss = 0.0
    correct_predictions = 0
    total_samples = 0
    
    with torch.no_grad():
        progress_bar = tqdm(dataloader, desc="Validation")
        
        for images, labels in progress_bar:
            images, labels = images.to(device), labels.to(device)
            
            # Forward pass
            outputs = model(images)
            loss = criterion(outputs, labels)
            
            # Statistics
            running_loss += loss.item()
            _, predicted = torch.max(outputs.data, 1)
            total_samples += labels.size(0)
            correct_predictions += (predicted == labels).sum().item()
            
            # Update progress bar
            progress_bar.set_postfix({
                'Loss': f'{loss.item():.4f}',
                'Acc': f'{100 * correct_predictions / total_samples:.2f}%'
            })
    
    epoch_loss = running_loss / len(dataloader)
    epoch_acc = correct_predictions / total_samples
    
    return epoch_loss, epoch_acc


def main():
    parser = argparse.ArgumentParser(description='Fine-tune a model for pigeon classification')
    parser.add_argument('--data_dir', type=str, default='data/raw', 
                        help='Directory containing pigeon image folders')
    parser.add_argument('--epochs', type=int, default=20, 
                        help='Number of training epochs')
    parser.add_argument('--batch_size', type=int, default=16, 
                        help='Batch size for training')
    parser.add_argument('--learning_rate', type=float, default=0.001, 
                        help='Learning rate')
    parser.add_argument('--model_path', type=str, default='src/pigeon_classifier/models/pigeon_classifier.pth', 
                        help='Path to save the trained model')
    parser.add_argument('--input_size', type=int, default=224, 
                        help='Input image size')
    parser.add_argument('--test_size', type=float, default=0.2, 
                        help='Fraction of data to use for validation')
    parser.add_argument('--no_augment', action='store_true', 
                        help='Disable data augmentation')
    
    args = parser.parse_args()
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"Using device: {device}")
    
    # Load data
    print("Loading pigeon data...")
    train_paths, train_labels, val_paths, val_labels, class_to_idx, idx_to_class = load_pigeon_data(
        args.data_dir, test_size=args.test_size
    )
    
    num_classes = len(class_to_idx)
    print(f"Found {num_classes} pigeon classes")
    print(f"Training samples: {len(train_paths)}")
    print(f"Validation samples: {len(val_paths)}")
    
    # Get transforms
    train_transform, val_transform = get_transforms(
        input_size=args.input_size, 
        augment=not args.no_augment
    )
    
    # Create datasets
    train_dataset = PigeonDataset(train_paths, train_labels, train_transform)
    val_dataset = PigeonDataset(val_paths, val_labels, val_transform)
    
    # Create data loaders
    train_loader = DataLoader(train_dataset, batch_size=args.batch_size, shuffle=True, num_workers=2)
    val_loader = DataLoader(val_dataset, batch_size=args.batch_size, shuffle=False, num_workers=2)
    
    # Create model
    print("Creating model...")
    model = create_model(num_classes)
    model = model.to(device)
    
    # Define loss function and optimizer
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=args.learning_rate)
    scheduler = optim.lr_scheduler.StepLR(optimizer, step_size=7, gamma=0.1)
    
    # Training loop
    print("Starting training...")
    best_val_acc = 0.0
    
    for epoch in range(args.epochs):
        print(f"\nEpoch {epoch + 1}/{args.epochs}")
        print("-" * 50)
        
        # Train
        train_loss, train_acc = train_epoch(model, train_loader, criterion, optimizer, device)
        
        # Validate
        val_loss, val_acc = validate_epoch(model, val_loader, criterion, device)
        
        # Update learning rate
        scheduler.step()
        
        # Print epoch results
        print(f"Train Loss: {train_loss:.4f}, Train Acc: {train_acc:.4f}")
        print(f"Val Loss: {val_loss:.4f}, Val Acc: {val_acc:.4f}")
        
        # Save best model
        if val_acc > best_val_acc:
            best_val_acc = val_acc
            save_model_and_mappings(model, class_to_idx, args.model_path)
            print(f"New best model saved! Validation accuracy: {val_acc:.4f}")
    
    print(f"\nTraining completed! Best validation accuracy: {best_val_acc:.4f}")
    print(f"Model saved to: {args.model_path}")


if __name__ == "__main__":
    main()
