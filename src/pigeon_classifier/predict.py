"""
Predict pigeon class for a given image.
"""
import argparse
import torch
from torchvision import models
import torch.nn as nn
from pathlib import Path

from .utils import (
    get_transforms,
    load_model_and_mappings,
    predict_image
)


def create_model(num_classes: int):
    """
    Create a ResNet50 model architecture.
    
    Args:
        num_classes: Number of pigeon classes
    
    Returns:
        model: PyTorch model (without weights loaded)
    """
    model = models.resnet50(pretrained=False)
    
    # Replace the final fully connected layer
    num_features = model.fc.in_features
    model.fc = nn.Linear(num_features, num_classes)
    
    return model


def format_predictions(predictions, show_all=False):
    """
    Format predictions for display.
    
    Args:
        predictions: List of (class_name, probability) tuples
        show_all: Whether to show all predictions or just top 5
    
    Returns:
        formatted_string: Nicely formatted prediction results
    """
    if not show_all:
        predictions = predictions[:5]  # Show top 5
    
    result = "\n🐦 PIGEON CLASSIFICATION RESULTS 🐦\n"
    result += "=" * 50 + "\n\n"
    
    for i, (pigeon_name, probability) in enumerate(predictions, 1):
        confidence = probability * 100
        
        # Add visual indicators for confidence levels
        if confidence > 80:
            indicator = "🟢"  # High confidence
        elif confidence > 50:
            indicator = "🟡"  # Medium confidence
        else:
            indicator = "🔴"  # Low confidence
        
        result += f"{i:2d}. {indicator} {pigeon_name:<20} {confidence:6.2f}%\n"
    
    result += "\n" + "=" * 50 + "\n"
    
    # Add interpretation
    top_confidence = predictions[0][1] * 100
    if top_confidence > 80:
        result += "🎯 High confidence prediction!\n"
    elif top_confidence > 50:
        result += "⚠️  Medium confidence - consider additional images for verification.\n"
    else:
        result += "❓ Low confidence - this pigeon might not be in the training dataset.\n"
    
    return result


def main():
    parser = argparse.ArgumentParser(description='Predict pigeon class for an image')
    parser.add_argument('--image_path', type=str, required=True,
                        help='Path to the image to classify')
    parser.add_argument('--model_path', type=str, 
                        default='src/pigeon_classifier/models/pigeon_classifier.pth',
                        help='Path to the trained model')
    parser.add_argument('--top_k', type=int, default=10,
                        help='Number of top predictions to show')
    parser.add_argument('--show_all', action='store_true',
                        help='Show all predictions instead of just top 5')
    parser.add_argument('--input_size', type=int, default=224,
                        help='Input image size (should match training)')
    
    args = parser.parse_args()
    
    # Check if image exists
    if not Path(args.image_path).exists():
        print(f"Error: Image file '{args.image_path}' not found!")
        return
    
    # Check if model exists
    if not Path(args.model_path).exists():
        print(f"Error: Model file '{args.model_path}' not found!")
        print("Please train the model first using: python -m pigeon_classifier.fine_tune")
        return
    
    # Set device
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    try:
        # Load class mappings first to get number of classes
        mappings_path = args.model_path.replace('.pth', '_mappings.json')
        if not Path(mappings_path).exists():
            print(f"Error: Class mappings file '{mappings_path}' not found!")
            return
        
        import json
        with open(mappings_path, 'r') as f:
            class_to_idx = json.load(f)
        
        num_classes = len(class_to_idx)
        
        # Create model architecture
        model = create_model(num_classes)
        
        # Load model and mappings
        model, class_to_idx, idx_to_class = load_model_and_mappings(model, args.model_path)
        model = model.to(device)
        
        # Get transform (no augmentation for inference)
        _, val_transform = get_transforms(input_size=args.input_size, augment=False)
        
        # Make prediction
        print(f"Analyzing image: {args.image_path}")
        print("Processing...")
        
        predictions = predict_image(
            model=model,
            image_path=args.image_path,
            idx_to_class=idx_to_class,
            transform=val_transform,
            device=device,
            top_k=args.top_k
        )
        
        # Display results
        formatted_results = format_predictions(predictions, show_all=args.show_all)
        print(formatted_results)
        
        # Save results to file (optional)
        results_path = Path(args.image_path).parent / f"{Path(args.image_path).stem}_predictions.txt"
        with open(results_path, 'w') as f:
            f.write(f"Predictions for: {args.image_path}\n")
            f.write(formatted_results)
        
        print(f"📄 Results also saved to: {results_path}")
        
    except Exception as e:
        print(f"Error during prediction: {str(e)}")
        print("Please make sure the model was trained properly and all files are in place.")


if __name__ == "__main__":
    main()
