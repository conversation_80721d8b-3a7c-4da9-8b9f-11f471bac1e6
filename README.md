# Pigeon Classifier 🐦

A deep learning system for recognizing individual pigeons using transfer learning. This system can be trained with minimal images per pigeon and provides confidence-ranked predictions.

## Features

- **Transfer Learning**: Uses pre-trained ResNet50 for efficient training with limited data
- **Data Augmentation**: Automatically applies augmentation to increase effective dataset size
- **Confidence Scoring**: Provides probability rankings for all pigeon classes
- **Easy Training**: Simple command-line interface for training
- **Readable Predictions**: Human-friendly output with confidence indicators

## Installation

1. Make sure you have Python 3.13+ installed
2. Install dependencies:

```bash
poetry install
```

## Dataset Structure

Your pigeon images should be organized in folders like this:

```
data/raw/
├── Angel/
│   ├── image1.jpg
│   ├── image2.jpg
│   └── ...
├── Anthony/
│   ├── image1.jpg
│   └── ...
├── Beau G<PERSON>ç<PERSON>/
│   └── ...
└── ... (other pigeon folders)
```

Each folder name represents a pigeon's name, and should contain images of that specific pigeon.

## Usage

### 1. Training the Model

Train the model using your pigeon dataset:

```bash
# Basic training (recommended settings)
python -m pigeon_classifier.fine_tune

# Custom training with specific parameters
python -m pigeon_classifier.fine_tune \
    --data_dir data/raw \
    --epochs 25 \
    --batch_size 16 \
    --learning_rate 0.001
```

**Training Parameters:**
- `--data_dir`: Directory containing pigeon folders (default: `data/raw`)
- `--epochs`: Number of training epochs (default: 20)
- `--batch_size`: Batch size for training (default: 16)
- `--learning_rate`: Learning rate (default: 0.001)
- `--model_path`: Where to save the trained model (default: `src/pigeon_classifier/models/pigeon_classifier.pth`)
- `--test_size`: Fraction of data for validation (default: 0.2)
- `--no_augment`: Disable data augmentation

### 2. Making Predictions

Classify a new pigeon image:

```bash
# Basic prediction
python -m pigeon_classifier.predict --image_path path/to/pigeon_image.jpg

# Show more predictions
python -m pigeon_classifier.predict \
    --image_path path/to/pigeon_image.jpg \
    --top_k 15 \
    --show_all
```

**Prediction Parameters:**
- `--image_path`: Path to the image to classify (required)
- `--model_path`: Path to trained model (default: `src/pigeon_classifier/models/pigeon_classifier.pth`)
- `--top_k`: Number of top predictions to calculate (default: 10)
- `--show_all`: Show all predictions instead of just top 5

### Example Output

```
🐦 PIGEON CLASSIFICATION RESULTS 🐦
==================================================

 1. 🟢 Angel                  87.34%
 2. 🟡 Anthony                 8.92%
 3. 🔴 Beau Garçon             2.15%
 4. 🔴 Cassandra              1.23%
 5. 🔴 Chanjon                 0.36%

==================================================
🎯 High confidence prediction!

📄 Results also saved to: pigeon_image_predictions.txt
```

## Model Details

- **Architecture**: ResNet50 with transfer learning
- **Input Size**: 224x224 pixels
- **Data Augmentation**: Random flips, rotations, color jittering, and affine transforms
- **Optimization**: Adam optimizer with learning rate scheduling
- **Validation**: 20% of data used for validation during training

## Tips for Best Results

1. **Image Quality**: Use clear, well-lit images of pigeons
2. **Multiple Angles**: Include images from different angles for each pigeon
3. **Consistent Lighting**: Try to have varied but reasonable lighting conditions
4. **Minimum Images**: At least 5-10 images per pigeon recommended
5. **Image Format**: Supports JPG, PNG, BMP, and TIFF formats

## Troubleshooting

### Model Not Found Error
If you get a "Model file not found" error when predicting:
```bash
# Make sure you've trained the model first
python -m pigeon_classifier.fine_tune
```

### CUDA/GPU Issues
The system automatically detects and uses GPU if available, but falls back to CPU. For CPU-only training, no additional setup is needed.

### Memory Issues
If you encounter memory issues during training:
- Reduce `--batch_size` (try 8 or 4)
- Reduce `--input_size` to 192 or 160

## File Structure

```
src/pigeon_classifier/
├── __init__.py           # Package initialization
├── fine_tune.py          # Training script
├── predict.py            # Prediction script
├── utils.py              # Utility functions
└── models/               # Saved models directory
    ├── __init__.py
    ├── pigeon_classifier.pth      # Trained model weights
    └── pigeon_classifier_mappings.json  # Class mappings
```